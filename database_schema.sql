-- ===================================================
-- نظام إدارة المهام للشركة - هيكل قاعدة البيانات
-- ===================================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS task_management 
CHARACTER SET utf8 
COLLATE utf8_general_ci;

USE task_management;

-- ===================================================
-- جدول المستخدمين (users)
-- ===================================================
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للمستخدم',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT 'اسم المستخدم',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT 'البريد الإلكتروني',
    password VARCHAR(255) NOT NULL COMMENT 'كلمة المرور المشفرة',
    full_name VARCHAR(100) NOT NULL COMMENT 'الاسم الكامل',
    role ENUM('admin', 'supervisor', 'employee') NOT NULL DEFAULT 'employee' COMMENT 'دور المستخدم',
    department VARCHAR(100) COMMENT 'القسم',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة المستخدم (نشط/غير نشط)'
) COMMENT 'جدول المستخدمين والموظفين';

-- ===================================================
-- جدول المهام (tasks)
-- ===================================================
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للمهمة',
    title VARCHAR(200) NOT NULL COMMENT 'عنوان المهمة',
    description TEXT COMMENT 'وصف المهمة',
    assigned_to INT NOT NULL COMMENT 'المكلف بالمهمة',
    assigned_by INT NOT NULL COMMENT 'من قام بالتكليف',
    supervisor_id INT COMMENT 'المشرف على المهمة',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT 'أولوية المهمة',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT 'حالة المهمة',
    start_date DATE COMMENT 'تاريخ بداية المهمة',
    due_date DATE COMMENT 'تاريخ انتهاء المهمة',
    completion_date DATETIME NULL COMMENT 'تاريخ إكمال المهمة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء المهمة',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    
    -- المفاتيح الخارجية
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (supervisor_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- الفهارس لتحسين الأداء
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_assigned_by (assigned_by),
    INDEX idx_supervisor_id (supervisor_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_due_date (due_date)
) COMMENT 'جدول المهام والتكليفات';

-- ===================================================
-- جدول تعليقات المهام (task_comments)
-- ===================================================
CREATE TABLE IF NOT EXISTS task_comments (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للتعليق',
    task_id INT NOT NULL COMMENT 'معرف المهمة',
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    comment TEXT NOT NULL COMMENT 'نص التعليق',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء التعليق',
    
    -- المفاتيح الخارجية
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- الفهارس
    INDEX idx_task_id (task_id),
    INDEX idx_user_id (user_id)
) COMMENT 'جدول تعليقات المهام';

-- ===================================================
-- جدول الإشعارات (notifications)
-- ===================================================
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الفريد للإشعار',
    user_id INT NOT NULL COMMENT 'معرف المستخدم المستقبل للإشعار',
    title VARCHAR(200) NOT NULL COMMENT 'عنوان الإشعار',
    message TEXT NOT NULL COMMENT 'محتوى الإشعار',
    type ENUM('task_assigned', 'task_completed', 'task_overdue', 'general') DEFAULT 'general' COMMENT 'نوع الإشعار',
    is_read BOOLEAN DEFAULT FALSE COMMENT 'حالة قراءة الإشعار',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء الإشعار',
    
    -- المفاتيح الخارجية
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- الفهارس
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_type (type)
) COMMENT 'جدول الإشعارات';

-- ===================================================
-- إدراج البيانات الافتراضية
-- ===================================================

-- إدراج المستخدم الأدمن الافتراضي
INSERT IGNORE INTO users (username, email, password, full_name, role) 
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin');

-- إدراج مستخدمين تجريبيين (اختياري)
INSERT IGNORE INTO users (username, email, password, full_name, role, department) VALUES
('supervisor1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد', 'supervisor', 'تقنية المعلومات'),
('employee1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'فاطمة علي', 'employee', 'تقنية المعلومات'),
('employee2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد سالم', 'employee', 'المحاسبة');

-- ===================================================
-- إنشاء Views مفيدة
-- ===================================================

-- عرض المهام مع تفاصيل المستخدمين
CREATE OR REPLACE VIEW tasks_with_users AS
SELECT 
    t.id,
    t.title,
    t.description,
    t.priority,
    t.status,
    t.start_date,
    t.due_date,
    t.completion_date,
    t.created_at,
    t.updated_at,
    u1.full_name AS assigned_to_name,
    u1.email AS assigned_to_email,
    u2.full_name AS assigned_by_name,
    u2.email AS assigned_by_email,
    u3.full_name AS supervisor_name,
    u3.email AS supervisor_email,
    CASE 
        WHEN t.due_date < CURDATE() AND t.status != 'completed' THEN TRUE
        ELSE FALSE
    END AS is_overdue
FROM tasks t
LEFT JOIN users u1 ON t.assigned_to = u1.id
LEFT JOIN users u2 ON t.assigned_by = u2.id
LEFT JOIN users u3 ON t.supervisor_id = u3.id;

-- عرض إحصائيات المهام لكل مستخدم
CREATE OR REPLACE VIEW user_task_stats AS
SELECT 
    u.id,
    u.full_name,
    u.role,
    COUNT(t.id) AS total_tasks,
    SUM(CASE WHEN t.status = 'completed' THEN 1 ELSE 0 END) AS completed_tasks,
    SUM(CASE WHEN t.status = 'in_progress' THEN 1 ELSE 0 END) AS in_progress_tasks,
    SUM(CASE WHEN t.status = 'pending' THEN 1 ELSE 0 END) AS pending_tasks,
    SUM(CASE WHEN t.due_date < CURDATE() AND t.status != 'completed' THEN 1 ELSE 0 END) AS overdue_tasks
FROM users u
LEFT JOIN tasks t ON u.id = t.assigned_to
WHERE u.is_active = TRUE
GROUP BY u.id, u.full_name, u.role;

-- ===================================================
-- إنشاء Stored Procedures مفيدة
-- ===================================================

DELIMITER //

-- إجراء لإنشاء مهمة جديدة مع إشعار
CREATE PROCEDURE CreateTaskWithNotification(
    IN p_title VARCHAR(200),
    IN p_description TEXT,
    IN p_assigned_to INT,
    IN p_assigned_by INT,
    IN p_supervisor_id INT,
    IN p_priority ENUM('low', 'medium', 'high', 'urgent'),
    IN p_start_date DATE,
    IN p_due_date DATE
)
BEGIN
    DECLARE task_id INT;
    
    -- إدراج المهمة
    INSERT INTO tasks (title, description, assigned_to, assigned_by, supervisor_id, priority, start_date, due_date)
    VALUES (p_title, p_description, p_assigned_to, p_assigned_by, p_supervisor_id, p_priority, p_start_date, p_due_date);
    
    SET task_id = LAST_INSERT_ID();
    
    -- إضافة إشعار للموظف
    INSERT INTO notifications (user_id, title, message, type)
    VALUES (p_assigned_to, 'مهمة جديدة', CONCAT('تم تكليفك بمهمة جديدة: ', p_title), 'task_assigned');
    
    -- إضافة إشعار للمشرف إذا وجد
    IF p_supervisor_id IS NOT NULL THEN
        INSERT INTO notifications (user_id, title, message, type)
        VALUES (p_supervisor_id, 'مهمة جديدة للإشراف', CONCAT('تم تكليفك بالإشراف على مهمة: ', p_title), 'task_assigned');
    END IF;
    
    SELECT task_id AS new_task_id;
END //

-- إجراء لإكمال مهمة مع إشعار
CREATE PROCEDURE CompleteTaskWithNotification(
    IN p_task_id INT,
    IN p_completed_by INT
)
BEGIN
    DECLARE task_title VARCHAR(200);
    DECLARE assigned_by_id INT;
    DECLARE supervisor_id INT;
    DECLARE user_name VARCHAR(100);
    
    -- الحصول على تفاصيل المهمة
    SELECT title, assigned_by, supervisor_id INTO task_title, assigned_by_id, supervisor_id
    FROM tasks WHERE id = p_task_id;
    
    -- الحصول على اسم المستخدم
    SELECT full_name INTO user_name FROM users WHERE id = p_completed_by;
    
    -- تحديث حالة المهمة
    UPDATE tasks 
    SET status = 'completed', completion_date = NOW(), updated_at = NOW()
    WHERE id = p_task_id;
    
    -- إضافة إشعار للمدير
    INSERT INTO notifications (user_id, title, message, type)
    VALUES (assigned_by_id, 'تم إكمال مهمة', CONCAT('تم إكمال المهمة: ', task_title, ' بواسطة ', user_name), 'task_completed');
    
    -- إضافة إشعار للمشرف إذا وجد
    IF supervisor_id IS NOT NULL THEN
        INSERT INTO notifications (user_id, title, message, type)
        VALUES (supervisor_id, 'تم إكمال مهمة', CONCAT('تم إكمال المهمة: ', task_title, ' بواسطة ', user_name), 'task_completed');
    END IF;
END //

DELIMITER ;

-- ===================================================
-- إنشاء Triggers للتحديث التلقائي
-- ===================================================

-- Trigger لتحديث تاريخ آخر تحديث في جدول المهام
DELIMITER //
CREATE TRIGGER update_task_timestamp 
    BEFORE UPDATE ON tasks
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = NOW();
END //
DELIMITER ;

-- ===================================================
-- إنشاء Events للمهام المتأخرة (اختياري)
-- ===================================================

-- تفعيل Event Scheduler
SET GLOBAL event_scheduler = ON;

-- Event للتحقق من المهام المتأخرة يومياً
DELIMITER //
CREATE EVENT IF NOT EXISTS check_overdue_tasks
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_DATE + INTERVAL 1 DAY
DO
BEGIN
    -- إضافة إشعارات للمهام المتأخرة
    INSERT INTO notifications (user_id, title, message, type)
    SELECT 
        t.assigned_to,
        'مهمة متأخرة',
        CONCAT('المهمة "', t.title, '" متأخرة عن موعد التسليم'),
        'task_overdue'
    FROM tasks t
    WHERE t.due_date < CURDATE() 
    AND t.status NOT IN ('completed', 'cancelled')
    AND NOT EXISTS (
        SELECT 1 FROM notifications n 
        WHERE n.user_id = t.assigned_to 
        AND n.type = 'task_overdue' 
        AND DATE(n.created_at) = CURDATE()
        AND n.message LIKE CONCAT('%', t.title, '%')
    );
END //
DELIMITER ;

-- ===================================================
-- تعليقات ومعلومات إضافية
-- ===================================================

/*
ملاحظات مهمة:

1. كلمة المرور الافتراضية للجميع: admin123
2. يتم تشفير كلمات المرور باستخدام password_hash() في PHP
3. جميع التواريخ والأوقات بالتوقيت المحلي للخادم
4. يدعم النظام اللغة العربية بترميز UTF-8
5. تم إنشاء فهارس لتحسين أداء الاستعلامات
6. العلاقات الخارجية محمية بـ CASCADE و SET NULL

الأدوار:
- admin: مدير النظام (صلاحيات كاملة)
- supervisor: مشرف (متابعة المهام)
- employee: موظف (تنفيذ المهام)

حالات المهام:
- pending: في الانتظار
- in_progress: قيد التنفيذ  
- completed: مكتملة
- cancelled: ملغية

أولويات المهام:
- low: منخفضة
- medium: متوسطة
- high: عالية
- urgent: عاجلة

أنواع الإشعارات:
- task_assigned: مهمة جديدة
- task_completed: مهمة مكتملة
- task_overdue: مهمة متأخرة
- general: عام
*/
