<?php
require_once dirname(__DIR__) . '/includes/auth.php';
require_once dirname(__DIR__) . '/config/database.php';

requireLogin();
requireRole('supervisor');

$auth = new Auth();
$user = $auth->getCurrentUser();
$database = new Database();
$conn = $database->getConnection();

// إحصائيات للمشرف
$stats = [];

// المهام التي يشرف عليها
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE supervisor_id = ?");
$stmt->execute([$user['id']]);
$stats['supervised_tasks'] = $stmt->fetch()['count'];

// المهام المكتملة
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE supervisor_id = ? AND status = 'completed'");
$stmt->execute([$user['id']]);
$stats['completed_tasks'] = $stmt->fetch()['count'];

// المهام قيد التنفيذ
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE supervisor_id = ? AND status = 'in_progress'");
$stmt->execute([$user['id']]);
$stats['in_progress_tasks'] = $stmt->fetch()['count'];

// المهام المتأخرة
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE supervisor_id = ? AND due_date < CURDATE() AND status != 'completed'");
$stmt->execute([$user['id']]);
$stats['overdue_tasks'] = $stmt->fetch()['count'];

// المهام التي يشرف عليها
$stmt = $conn->prepare("
    SELECT t.*,
           u1.full_name as assigned_to_name,
           u2.full_name as assigned_by_name
    FROM tasks t
    LEFT JOIN users u1 ON t.assigned_to = u1.id
    LEFT JOIN users u2 ON t.assigned_by = u2.id
    WHERE t.supervisor_id = ?
    ORDER BY
        CASE
            WHEN t.status = 'pending' THEN 1
            WHEN t.status = 'in_progress' THEN 2
            WHEN t.status = 'completed' THEN 3
            WHEN t.status = 'cancelled' THEN 4
        END,
        t.due_date ASC
");
$stmt->execute([$user['id']]);
$supervised_tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

// الإشعارات الحديثة
$stmt = $conn->prepare("
    SELECT * FROM notifications
    WHERE user_id = ?
    ORDER BY created_at DESC
    LIMIT 5
");
$stmt->execute([$user['id']]);
$notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

// معالجة تحديث حالة المهمة (للمشرف)
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_task_status'])) {
    $task_id = $_POST['task_id'];
    $new_status = $_POST['status'];

    try {
        $stmt = $conn->prepare("UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND supervisor_id = ?");
        $stmt->execute([$new_status, $task_id, $user['id']]);

        $message = 'تم تحديث حالة المهمة بنجاح';
        header('Location: supervisor.php?success=1');
        exit();

    } catch(PDOException $e) {
        $message = 'خطأ في تحديث المهمة: ' . $e->getMessage();
    }
}

// رسالة النجاح من URL
if (isset($_GET['success'])) {
    $message = 'تم تحديث حالة المهمة بنجاح';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المشرف - نظام إدارة المهام</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow-lg);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
        }
        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color), #059669);
        }
        .stat-card.danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
        }
        .stat-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .task-card {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 15px;
            overflow: hidden;
            border-right: 4px solid var(--primary-color);
        }
        .task-card.overdue {
            border-right-color: var(--danger-color);
        }
        .task-card.completed {
            border-right-color: var(--success-color);
            opacity: 0.8;
        }
        .task-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .task-title {
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }
        .task-body {
            padding: 20px;
        }
        .task-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .meta-item {
            font-size: 14px;
        }
        .meta-label {
            color: var(--text-light);
            font-weight: 500;
        }
        .meta-value {
            color: var(--text-dark);
            margin-top: 2px;
        }
        .notification-item {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .notification-item:last-child {
            border-bottom: none;
        }
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        .notification-icon.success {
            background: var(--success-color);
            color: white;
        }
        .notification-content {
            flex: 1;
        }
        .notification-title {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 5px;
        }
        .notification-message {
            color: var(--text-light);
            font-size: 14px;
        }
        .notification-time {
            font-size: 12px;
            color: var(--text-light);
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- شريط التنقل -->
        <nav class="navbar">
            <div class="navbar-content">
                <a href="#" class="navbar-brand">📋 نظام إدارة المهام</a>
                <div class="navbar-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($user['full_name']); ?></div>
                        <div class="user-role">مشرف</div>
                    </div>
                    <a href="../includes/logout.php" class="btn btn-danger">تسجيل الخروج</a>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">لوحة تحكم المشرف</h1>
                <p class="page-subtitle">مرحباً <?php echo htmlspecialchars($user['full_name']); ?>، إليك المهام التي تشرف عليها</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- الإحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['supervised_tasks']; ?></div>
                    <div class="stat-label">المهام المشرف عليها</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number"><?php echo $stats['in_progress_tasks']; ?></div>
                    <div class="stat-label">قيد التنفيذ</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-number"><?php echo $stats['completed_tasks']; ?></div>
                    <div class="stat-label">مكتملة</div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-number"><?php echo $stats['overdue_tasks']; ?></div>
                    <div class="stat-label">متأخرة</div>
                </div>
            </div>

            <div class="dashboard-grid">
                <!-- المهام المشرف عليها -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">المهام المشرف عليها</h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($supervised_tasks)): ?>
                            <div class="text-center">
                                <p>لا توجد مهام تشرف عليها حالياً</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($supervised_tasks as $task): ?>
                                <?php
                                $is_overdue = $task['due_date'] && $task['due_date'] < date('Y-m-d') && $task['status'] !== 'completed';
                                $card_class = '';
                                if ($is_overdue) $card_class = 'overdue';
                                if ($task['status'] === 'completed') $card_class = 'completed';
                                ?>
                                <div class="task-card <?php echo $card_class; ?>">
                                    <div class="task-header">
                                        <h4 class="task-title"><?php echo htmlspecialchars($task['title']); ?></h4>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch ($task['status']) {
                                            case 'pending':
                                                $status_class = 'badge-warning';
                                                $status_text = 'في الانتظار';
                                                break;
                                            case 'in_progress':
                                                $status_class = 'badge-secondary';
                                                $status_text = 'قيد التنفيذ';
                                                break;
                                            case 'completed':
                                                $status_class = 'badge-success';
                                                $status_text = 'مكتملة';
                                                break;
                                            case 'cancelled':
                                                $status_class = 'badge-danger';
                                                $status_text = 'ملغية';
                                                break;
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </div>
                                    <div class="task-body">
                                        <?php if ($task['description']): ?>
                                            <p style="margin-bottom: 15px; color: var(--text-dark);">
                                                <?php echo nl2br(htmlspecialchars($task['description'])); ?>
                                            </p>
                                        <?php endif; ?>

                                        <div class="task-meta">
                                            <div class="meta-item">
                                                <div class="meta-label">المكلف</div>
                                                <div class="meta-value"><?php echo htmlspecialchars($task['assigned_to_name']); ?></div>
                                            </div>
                                            <div class="meta-item">
                                                <div class="meta-label">مكلف من</div>
                                                <div class="meta-value"><?php echo htmlspecialchars($task['assigned_by_name']); ?></div>
                                            </div>
                                            <div class="meta-item">
                                                <div class="meta-label">تاريخ الانتهاء</div>
                                                <div class="meta-value <?php echo $is_overdue ? 'text-danger' : ''; ?>">
                                                    <?php echo $task['due_date'] ? date('Y-m-d', strtotime($task['due_date'])) : 'غير محدد'; ?>
                                                    <?php if ($is_overdue): ?>
                                                        <span style="color: var(--danger-color); font-weight: bold;"> (متأخرة)</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="meta-item">
                                                <div class="meta-label">الأولوية</div>
                                                <div class="meta-value">
                                                    <?php
                                                    $priority_text = '';
                                                    switch ($task['priority']) {
                                                        case 'low': $priority_text = 'منخفضة'; break;
                                                        case 'medium': $priority_text = 'متوسطة'; break;
                                                        case 'high': $priority_text = 'عالية'; break;
                                                        case 'urgent': $priority_text = 'عاجلة'; break;
                                                    }
                                                    echo $priority_text;
                                                    ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- الإشعارات -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الإشعارات الحديثة</h3>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <?php if (empty($notifications)): ?>
                            <div class="text-center" style="padding: 20px;">
                                <p>لا توجد إشعارات جديدة</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($notifications as $notification): ?>
                                <div class="notification-item">
                                    <div class="notification-icon success">
                                        <?php
                                        switch ($notification['type']) {
                                            case 'task_completed':
                                                echo '✅';
                                                break;
                                            case 'task_assigned':
                                                echo '📋';
                                                break;
                                            case 'task_overdue':
                                                echo '⚠️';
                                                break;
                                            default:
                                                echo '📢';
                                                break;
                                        }
                                        ?>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                        <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                                        <div class="notification-time"><?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
