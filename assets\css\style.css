/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-bg: #f8fafc;
    --white: #ffffff;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-dark);
    direction: rtl;
    text-align: right;
}

/* صفحة تسجيل الدخول */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-card {
    background: var(--white);
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    width: 100%;
    max-width: 400px;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: 40px 30px;
    text-align: center;
}

.login-header h1 {
    font-size: 28px;
    margin-bottom: 10px;
    font-weight: 600;
}

.login-header p {
    opacity: 0.9;
    font-size: 16px;
}

.login-form {
    padding: 40px 30px;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
    background: var(--white);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    min-width: 120px;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    width: 100%;
    padding: 14px;
    font-size: 18px;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

/* رسائل التنبيه */
.alert {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d1fae5;
    border-color: #a7f3d0;
    color: #065f46;
}

.alert-danger {
    background: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-warning {
    background: #fef3c7;
    border-color: #fde68a;
    color: #92400e;
}

/* لوحة التحكم */
.dashboard {
    min-height: 100vh;
    background: var(--light-bg);
}

.navbar {
    background: var(--white);
    box-shadow: var(--shadow);
    padding: 0;
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.navbar-brand {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    text-align: left;
}

.user-name {
    font-weight: 500;
    color: var(--text-dark);
}

.user-role {
    font-size: 14px;
    color: var(--text-light);
}

.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px;
}

.page-header {
    margin-bottom: 30px;
}

.page-title {
    font-size: 32px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.page-subtitle {
    color: var(--text-light);
    font-size: 16px;
}

/* البطاقات */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 20px;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background: var(--light-bg);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-dark);
}

.card-body {
    padding: 25px;
}

/* الجداول */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--light-bg);
    font-weight: 600;
    color: var(--text-dark);
}

.table tbody tr:hover {
    background: var(--light-bg);
}

/* الشارات */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
    text-transform: uppercase;
}

.badge-success {
    background: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background: var(--warning-color);
    color: var(--white);
}

.badge-danger {
    background: var(--danger-color);
    color: var(--white);
}

.badge-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .navbar-content {
        padding: 15px 20px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .login-card {
        margin: 10px;
    }
    
    .login-header,
    .login-form {
        padding: 30px 20px;
    }
    
    .table-responsive {
        font-size: 14px;
    }
    
    .navbar-user {
        flex-direction: column;
        gap: 10px;
    }
}

/* تحسينات إضافية */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.text-center {
    text-align: center;
}

.mt-3 { margin-top: 1rem; }
.mb-3 { margin-bottom: 1rem; }
.mr-2 { margin-right: 0.5rem; }
.ml-2 { margin-left: 0.5rem; }

.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.gap-2 {
    gap: 0.5rem;
}
