<?php
require_once __DIR__ . '/config/database.php';

// إنشاء قاعدة البيانات
$database = new Database();
$database->createDatabase();
$conn = $database->getConnection();

if (!$conn) {
    die("فشل في الاتصال بقاعدة البيانات");
}

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>معلومات قاعدة البيانات - نظام إدارة المهام</title>";
echo "<link rel='stylesheet' href='assets/css/style.css'>";
echo "<style>";
echo "body { font-family: 'Cairo', sans-serif; background: var(--light-bg); padding: 20px; }";
echo ".container { max-width: 1200px; margin: 0 auto; }";
echo ".db-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".db-title { color: var(--primary-color); border-bottom: 2px solid var(--primary-color); padding-bottom: 10px; margin-bottom: 20px; }";
echo ".table-info { margin: 15px 0; }";
echo ".field { background: #f8f9fa; padding: 8px; margin: 5px 0; border-radius: 4px; }";
echo ".field-name { font-weight: bold; color: var(--primary-color); }";
echo ".field-type { color: var(--secondary-color); font-size: 14px; }";
echo ".stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }";
echo ".stat-box { background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); color: white; padding: 20px; border-radius: 8px; text-align: center; }";
echo ".stat-number { font-size: 24px; font-weight: bold; }";
echo ".stat-label { font-size: 14px; opacity: 0.9; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 style='text-align: center; color: var(--primary-color);'>📊 معلومات قاعدة البيانات - نظام إدارة المهام</h1>";

// إحصائيات قاعدة البيانات
echo "<div class='db-section'>";
echo "<h2 class='db-title'>📈 إحصائيات قاعدة البيانات</h2>";

$stats = [];

// عدد المستخدمين
try {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
    $stats['users'] = $stmt->fetch()['count'];
} catch(Exception $e) {
    $stats['users'] = 0;
}

// عدد المهام
try {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM tasks");
    $stats['tasks'] = $stmt->fetch()['count'];
} catch(Exception $e) {
    $stats['tasks'] = 0;
}

// عدد الإشعارات
try {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM notifications");
    $stats['notifications'] = $stmt->fetch()['count'];
} catch(Exception $e) {
    $stats['notifications'] = 0;
}

// عدد التعليقات
try {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM task_comments");
    $stats['comments'] = $stmt->fetch()['count'];
} catch(Exception $e) {
    $stats['comments'] = 0;
}

echo "<div class='stats-grid'>";
echo "<div class='stat-box'>";
echo "<div class='stat-number'>{$stats['users']}</div>";
echo "<div class='stat-label'>المستخدمين</div>";
echo "</div>";
echo "<div class='stat-box'>";
echo "<div class='stat-number'>{$stats['tasks']}</div>";
echo "<div class='stat-label'>المهام</div>";
echo "</div>";
echo "<div class='stat-box'>";
echo "<div class='stat-number'>{$stats['notifications']}</div>";
echo "<div class='stat-label'>الإشعارات</div>";
echo "</div>";
echo "<div class='stat-box'>";
echo "<div class='stat-number'>{$stats['comments']}</div>";
echo "<div class='stat-label'>التعليقات</div>";
echo "</div>";
echo "</div>";
echo "</div>";

// معلومات الجداول
$tables = [
    'users' => 'جدول المستخدمين',
    'tasks' => 'جدول المهام',
    'task_comments' => 'جدول تعليقات المهام',
    'notifications' => 'جدول الإشعارات'
];

foreach ($tables as $table => $title) {
    echo "<div class='db-section'>";
    echo "<h2 class='db-title'>🗂️ {$title} ({$table})</h2>";

    try {
        // الحصول على هيكل الجدول
        $stmt = $conn->query("DESCRIBE {$table}");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<div class='table-info'>";
        echo "<h3>📋 هيكل الجدول:</h3>";

        foreach ($columns as $column) {
            echo "<div class='field'>";
            echo "<span class='field-name'>{$column['Field']}</span> - ";
            echo "<span class='field-type'>{$column['Type']}</span>";
            if ($column['Null'] == 'NO') echo " <strong>(مطلوب)</strong>";
            if ($column['Key'] == 'PRI') echo " <strong style='color: #e74c3c;'>(مفتاح أساسي)</strong>";
            if ($column['Key'] == 'MUL') echo " <strong style='color: #f39c12;'>(مفتاح خارجي)</strong>";
            if ($column['Default']) echo " <em>(افتراضي: {$column['Default']})</em>";
            echo "</div>";
        }

        // عدد السجلات
        $stmt = $conn->query("SELECT COUNT(*) as count FROM {$table}");
        $count = $stmt->fetch()['count'];
        echo "<p><strong>📊 عدد السجلات:</strong> {$count}</p>";

        // عرض بعض البيانات النموذجية
        if ($count > 0) {
            echo "<h3>📄 عينة من البيانات:</h3>";
            $stmt = $conn->query("SELECT * FROM {$table} LIMIT 3");
            $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($sample_data)) {
                echo "<div style='overflow-x: auto;'>";
                echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";

                // رؤوس الجدول
                echo "<tr style='background: var(--primary-color); color: white;'>";
                foreach (array_keys($sample_data[0]) as $header) {
                    echo "<th style='padding: 8px; border: 1px solid #ddd; text-align: right;'>{$header}</th>";
                }
                echo "</tr>";

                // البيانات
                foreach ($sample_data as $row) {
                    echo "<tr>";
                    foreach ($row as $value) {
                        $display_value = $value;
                        if (strlen($value) > 50) {
                            $display_value = substr($value, 0, 50) . '...';
                        }
                        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: right;'>" . htmlspecialchars($display_value) . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
                echo "</div>";
            }
        }

        echo "</div>";

    } catch(Exception $e) {
        echo "<p style='color: red;'>خطأ في قراءة الجدول: " . $e->getMessage() . "</p>";
    }

    echo "</div>";
}

// معلومات الاتصال
echo "<div class='db-section'>";
echo "<h2 class='db-title'>🔧 إعدادات الاتصال</h2>";
echo "<div class='field'><span class='field-name'>الخادم:</span> localhost</div>";
echo "<div class='field'><span class='field-name'>اسم قاعدة البيانات:</span> task_management</div>";
echo "<div class='field'><span class='field-name'>اسم المستخدم:</span> root</div>";
echo "<div class='field'><span class='field-name'>ترميز الأحرف:</span> UTF-8</div>";
echo "<div class='field'><span class='field-name'>حالة الاتصال:</span> <span style='color: green; font-weight: bold;'>متصل ✅</span></div>";
echo "</div>";

// بيانات الدخول الافتراضية
echo "<div class='db-section'>";
echo "<h2 class='db-title'>🔑 بيانات الدخول الافتراضية</h2>";
echo "<div class='field'><span class='field-name'>المدير - اسم المستخدم:</span> admin</div>";
echo "<div class='field'><span class='field-name'>المدير - البريد الإلكتروني:</span> <EMAIL></div>";
echo "<div class='field'><span class='field-name'>كلمة المرور:</span> admin123</div>";
echo "<p style='color: var(--warning-color); font-weight: bold;'>⚠️ يُنصح بتغيير كلمة المرور الافتراضية بعد أول تسجيل دخول</p>";
echo "</div>";

// روابط مفيدة
echo "<div class='db-section'>";
echo "<h2 class='db-title'>🔗 روابط مفيدة</h2>";
echo "<p><a href='index.php' style='color: var(--primary-color); text-decoration: none; font-weight: bold;'>🏠 العودة للصفحة الرئيسية</a></p>";
echo "<p><a href='dashboard/' style='color: var(--primary-color); text-decoration: none; font-weight: bold;'>📊 لوحة التحكم</a></p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
