<?php
// إعدادات قاعدة البيانات
class Database {
    private $host = 'localhost';
    private $db_name = 'task_management';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        
        return $this->conn;
    }

    // إنشاء قاعدة البيانات والجداول
    public function createDatabase() {
        try {
            // الاتصال بدون تحديد قاعدة بيانات
            $conn = new PDO(
                "mysql:host=" . $this->host . ";charset=utf8",
                $this->username,
                $this->password
            );
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // إنشاء قاعدة البيانات
            $sql = "CREATE DATABASE IF NOT EXISTS " . $this->db_name . " CHARACTER SET utf8 COLLATE utf8_general_ci";
            $conn->exec($sql);

            // الاتصال بقاعدة البيانات الجديدة
            $this->conn = $this->getConnection();

            // إنشاء جدول المستخدمين
            $sql = "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role ENUM('admin', 'supervisor', 'employee') NOT NULL DEFAULT 'employee',
                department VARCHAR(100),
                phone VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE
            )";
            $this->conn->exec($sql);

            // إنشاء جدول المهام
            $sql = "CREATE TABLE IF NOT EXISTS tasks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(200) NOT NULL,
                description TEXT,
                assigned_to INT NOT NULL,
                assigned_by INT NOT NULL,
                supervisor_id INT,
                priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
                start_date DATE,
                due_date DATE,
                completion_date DATETIME NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (supervisor_id) REFERENCES users(id) ON DELETE SET NULL
            )";
            $this->conn->exec($sql);

            // إنشاء جدول تعليقات المهام
            $sql = "CREATE TABLE IF NOT EXISTS task_comments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                task_id INT NOT NULL,
                user_id INT NOT NULL,
                comment TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )";
            $this->conn->exec($sql);

            // إنشاء جدول الإشعارات
            $sql = "CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                type ENUM('task_assigned', 'task_completed', 'task_overdue', 'general') DEFAULT 'general',
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )";
            $this->conn->exec($sql);

            // إدراج المستخدم الأدمن الافتراضي
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $sql = "INSERT IGNORE INTO users (username, email, password, full_name, role) 
                    VALUES ('admin', '<EMAIL>', ?, 'مدير النظام', 'admin')";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$admin_password]);

            return true;
        } catch(PDOException $exception) {
            echo "خطأ في إنشاء قاعدة البيانات: " . $exception->getMessage();
            return false;
        }
    }
}
?>
