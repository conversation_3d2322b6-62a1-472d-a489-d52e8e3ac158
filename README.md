# نظام إدارة المهام للشركة

نظام شامل لإدارة المهام والتكليفات في الشركة مع ثلاثة أدوار مختلفة: المدير، المشرف، والموظف.

## المميزات

### 🔐 نظام المصادقة والأدوار
- **المدير (Admin)**: صلاحية كاملة لإدارة النظام وإنشاء المهام
- **المشرف (Supervisor)**: متابعة المهام المكلف بالإشراف عليها
- **الموظف (Employee)**: استقبال المهام وتحديث حالة التنفيذ

### 📋 إدارة المهام
- إنشاء مهام جديدة مع تحديد الموظف والمشرف
- تتبع حالة المهام (في الانتظار، قيد التنفيذ، مكتملة، ملغية)
- تحديد أولوية المهام (منخفضة، متوسطة، عالية، عاجلة)
- تحديد تواريخ البداية والانتهاء
- إشعارات تلقائية عند إنشاء أو إكمال المهام

### 🎨 التصميم
- واجهة حديثة ومتجاوبة مع جميع الشاشات
- تصميم باللغة العربية مع دعم RTL
- ألوان متناسقة وتجربة مستخدم ممتازة
- رسوم متحركة وتأثيرات بصرية

## متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx) أو PHP Built-in Server

## طريقة التشغيل

### 1. التشغيل السريع (باستخدام PHP Built-in Server)

```bash
# تشغيل الخادم على المنفذ 8080
php -S localhost:8080
```

أو استخدم الملف المرفق:
```bash
# في Windows
start_server.bat

# في Linux/Mac
php -S localhost:8080
```

### 2. التشغيل باستخدام XAMPP

1. انسخ المجلد إلى `htdocs` في XAMPP
2. شغل Apache و MySQL من لوحة تحكم XAMPP
3. افتح المتصفح على `http://localhost/Task.1`

## إعداد قاعدة البيانات

النظام يقوم بإنشاء قاعدة البيانات والجداول تلقائياً عند أول تشغيل.

### بيانات الدخول الافتراضية

**المدير:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

أو:
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

## هيكل المشروع

```
Task.1/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── includes/
│   ├── auth.php             # نظام المصادقة
│   └── logout.php           # تسجيل الخروج
├── dashboard/
│   ├── index.php            # توجيه حسب الدور
│   ├── admin.php            # لوحة تحكم المدير
│   ├── supervisor.php       # لوحة تحكم المشرف
│   ├── employee.php         # لوحة تحكم الموظف
│   └── tasks.php            # إدارة المهام
├── assets/
│   └── css/
│       └── style.css        # ملف التصميم الرئيسي
├── index.php                # صفحة تسجيل الدخول
├── start_server.bat         # ملف تشغيل الخادم
└── README.md               # هذا الملف
```

## قاعدة البيانات

### الجداول الرئيسية

1. **users** - معلومات المستخدمين والأدوار
2. **tasks** - المهام والتكليفات
3. **task_comments** - تعليقات المهام
4. **notifications** - الإشعارات

## الاستخدام

### للمدير:
1. تسجيل الدخول باستخدام بيانات المدير
2. إضافة مستخدمين جدد (موظفين ومشرفين)
3. إنشاء مهام جديدة وتوزيعها على الموظفين
4. متابعة تقدم جميع المهام
5. عرض التقارير والإحصائيات

### للمشرف:
1. تسجيل الدخول باستخدام بيانات المشرف
2. متابعة المهام المكلف بالإشراف عليها
3. استقبال إشعارات عند إكمال المهام
4. مراجعة تقدم الموظفين

### للموظف:
1. تسجيل الدخول باستخدام بيانات الموظف
2. عرض المهام المكلف بها
3. تحديث حالة المهام (بدء التنفيذ، إكمال)
4. متابعة المهام المتأخرة والمكتملة

## المميزات التقنية

- **أمان**: حماية من SQL Injection باستخدام Prepared Statements
- **جلسات آمنة**: إدارة جلسات المستخدمين بشكل آمن
- **تصميم متجاوب**: يعمل على جميع الأجهزة والشاشات
- **قاعدة بيانات محسنة**: فهارس وعلاقات محسنة للأداء
- **إشعارات فورية**: نظام إشعارات متقدم
- **واجهة سهلة**: تصميم بديهي وسهل الاستخدام

## الدعم والتطوير

هذا النظام قابل للتطوير والتخصيص حسب احتياجات الشركة. يمكن إضافة المزيد من المميزات مثل:

- تقارير مفصلة
- نظام رفع الملفات
- تعليقات على المهام
- تقويم المهام
- إشعارات البريد الإلكتروني
- API للتطبيقات المحمولة

## الترخيص

هذا المشروع مطور خصيصاً لاحتياجات الشركة ويمكن تخصيصه وتطويره حسب المتطلبات.
