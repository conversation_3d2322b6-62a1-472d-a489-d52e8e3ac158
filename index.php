<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

// إنشاء قاعدة البيانات والجداول إذا لم تكن موجودة
$database = new Database();
$database->createDatabase();

$auth = new Auth();
$error_message = '';
$success_message = '';

// إذا كان المستخدم مسجل دخول، توجيهه للوحة التحكم
if ($auth->isLoggedIn()) {
    header('Location: dashboard/');
    exit();
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $result = $auth->login($username, $password);
        
        if ($result['success']) {
            $success_message = $result['message'];
            // إعادة توجيه بعد ثانيتين
            header('refresh:2;url=dashboard/');
        } else {
            $error_message = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المهام - تسجيل الدخول</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            opacity: 0.1;
            z-index: -1;
        }
        
        .company-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
        }
        
        .login-footer {
            text-align: center;
            padding: 20px;
            background: var(--light-bg);
            color: var(--text-light);
            font-size: 14px;
        }
        
        .demo-credentials {
            background: #e0f2fe;
            border: 1px solid #b3e5fc;
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .demo-credentials h4 {
            color: #0277bd;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .demo-credentials p {
            margin: 5px 0;
            color: #01579b;
        }
        
        .password-toggle {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-light);
            font-size: 18px;
        }
        
        .form-group.password-field {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="company-logo">
                    📋
                </div>
                <h1>نظام إدارة المهام</h1>
                <p>مرحباً بك في نظام إدارة مهام الشركة</p>
            </div>
            
            <div class="login-form">
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <?php echo htmlspecialchars($success_message); ?>
                        <br><small>جاري التوجيه للوحة التحكم...</small>
                    </div>
                <?php endif; ?>
                
                <div class="demo-credentials">
                    <h4>🔑 بيانات تجريبية للدخول:</h4>
                    <p><strong>المدير:</strong> admin / admin123</p>
                    <p><strong>أو استخدم:</strong> <EMAIL> / admin123</p>
                </div>
                
                <form method="POST" action="">
                    <div class="form-group">
                        <label for="username">اسم المستخدم أو البريد الإلكتروني</label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="form-control" 
                            placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                            value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                            required
                            autocomplete="username"
                        >
                    </div>
                    
                    <div class="form-group password-field">
                        <label for="password">كلمة المرور</label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            placeholder="أدخل كلمة المرور"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            👁️
                        </button>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        تسجيل الدخول
                    </button>
                </form>
            </div>
            
            <div class="login-footer">
                <p>&copy; 2024 نظام إدارة المهام. جميع الحقوق محفوظة.</p>
                <p>تم التطوير بواسطة فريق تقنية المعلومات</p>
            </div>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleButton = document.querySelector('.password-toggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleButton.textContent = '🙈';
            } else {
                passwordField.type = 'password';
                toggleButton.textContent = '👁️';
            }
        }
        
        // تركيز تلقائي على حقل اسم المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('username');
            if (usernameField && !usernameField.value) {
                usernameField.focus();
            }
        });
        
        // إضافة تأثيرات بصرية
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
