<?php
require_once '../includes/auth.php';
require_once '../config/database.php';

requireLogin();
requireRole('admin');

$auth = new Auth();
$user = $auth->getCurrentUser();
$database = new Database();
$conn = $database->getConnection();

// إحصائيات سريعة
$stats = [];

// عدد المستخدمين
$stmt = $conn->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
$stats['users'] = $stmt->fetch()['count'];

// عدد المهام
$stmt = $conn->query("SELECT COUNT(*) as count FROM tasks");
$stats['total_tasks'] = $stmt->fetch()['count'];

// المهام المكتملة
$stmt = $conn->query("SELECT COUNT(*) as count FROM tasks WHERE status = 'completed'");
$stats['completed_tasks'] = $stmt->fetch()['count'];

// المهام المتأخرة
$stmt = $conn->query("SELECT COUNT(*) as count FROM tasks WHERE due_date < CURDATE() AND status != 'completed'");
$stats['overdue_tasks'] = $stmt->fetch()['count'];

// أحدث المهام
$stmt = $conn->prepare("
    SELECT t.*, u1.full_name as assigned_to_name, u2.full_name as assigned_by_name 
    FROM tasks t 
    LEFT JOIN users u1 ON t.assigned_to = u1.id 
    LEFT JOIN users u2 ON t.assigned_by = u2.id 
    ORDER BY t.created_at DESC 
    LIMIT 5
");
$stmt->execute();
$recent_tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

// معالجة إنشاء مستخدم جديد
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_user'])) {
    $result = $auth->createUser([
        'username' => $_POST['username'],
        'email' => $_POST['email'],
        'password' => $_POST['password'],
        'full_name' => $_POST['full_name'],
        'role' => $_POST['role'],
        'department' => $_POST['department'],
        'phone' => $_POST['phone']
    ]);
    
    $message = $result['message'];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - نظام إدارة المهام</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow-lg);
        }
        .stat-number {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 16px;
            opacity: 0.9;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .action-btn {
            display: block;
            padding: 15px 20px;
            background: var(--white);
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            text-decoration: none;
            border-radius: var(--border-radius);
            text-align: center;
            font-weight: 500;
            transition: var(--transition);
        }
        .action-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: var(--white);
            margin: 5% auto;
            padding: 0;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 500px;
            box-shadow: var(--shadow-lg);
        }
        .modal-header {
            padding: 20px 25px;
            background: var(--primary-color);
            color: var(--white);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }
        .modal-body {
            padding: 25px;
        }
        .close {
            color: var(--white);
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- شريط التنقل -->
        <nav class="navbar">
            <div class="navbar-content">
                <a href="#" class="navbar-brand">📋 نظام إدارة المهام</a>
                <div class="navbar-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($user['full_name']); ?></div>
                        <div class="user-role">مدير النظام</div>
                    </div>
                    <a href="../includes/logout.php" class="btn btn-danger">تسجيل الخروج</a>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">لوحة تحكم المدير</h1>
                <p class="page-subtitle">مرحباً <?php echo htmlspecialchars($user['full_name']); ?>، إليك نظرة عامة على النظام</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- الإحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['users']; ?></div>
                    <div class="stat-label">المستخدمين النشطين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_tasks']; ?></div>
                    <div class="stat-label">إجمالي المهام</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['completed_tasks']; ?></div>
                    <div class="stat-label">المهام المكتملة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['overdue_tasks']; ?></div>
                    <div class="stat-label">المهام المتأخرة</div>
                </div>
            </div>

            <!-- الإجراءات السريعة -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">الإجراءات السريعة</h3>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <a href="#" class="action-btn" onclick="openModal('userModal')">➕ إضافة مستخدم جديد</a>
                        <a href="tasks.php" class="action-btn">📋 إدارة المهام</a>
                        <a href="users.php" class="action-btn">👥 إدارة المستخدمين</a>
                        <a href="reports.php" class="action-btn">📊 التقارير</a>
                    </div>
                </div>
            </div>

            <!-- أحدث المهام -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">أحدث المهام</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_tasks)): ?>
                        <p class="text-center">لا توجد مهام حالياً</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>عنوان المهمة</th>
                                        <th>المكلف</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_tasks as $task): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($task['title']); ?></td>
                                            <td><?php echo htmlspecialchars($task['assigned_to_name']); ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';
                                                switch ($task['status']) {
                                                    case 'pending':
                                                        $status_class = 'badge-warning';
                                                        $status_text = 'في الانتظار';
                                                        break;
                                                    case 'in_progress':
                                                        $status_class = 'badge-secondary';
                                                        $status_text = 'قيد التنفيذ';
                                                        break;
                                                    case 'completed':
                                                        $status_class = 'badge-success';
                                                        $status_text = 'مكتملة';
                                                        break;
                                                    case 'cancelled':
                                                        $status_class = 'badge-danger';
                                                        $status_text = 'ملغية';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($task['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مستخدم -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="closeModal('userModal')">&times;</span>
                <h2>إضافة مستخدم جديد</h2>
            </div>
            <div class="modal-body">
                <form method="POST">
                    <div class="form-group">
                        <label for="username">اسم المستخدم</label>
                        <input type="text" id="username" name="username" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="full_name">الاسم الكامل</label>
                        <input type="text" id="full_name" name="full_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <input type="password" id="password" name="password" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="role">الدور</label>
                        <select id="role" name="role" class="form-control" required>
                            <option value="employee">موظف</option>
                            <option value="supervisor">مشرف</option>
                            <option value="admin">مدير</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="department">القسم</label>
                        <input type="text" id="department" name="department" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="phone">رقم الهاتف</label>
                        <input type="tel" id="phone" name="phone" class="form-control">
                    </div>
                    <button type="submit" name="create_user" class="btn btn-primary">إضافة المستخدم</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
