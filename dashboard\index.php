<?php
require_once dirname(__DIR__) . '/includes/auth.php';
requireLogin();

$auth = new Auth();
$user = $auth->getCurrentUser();

// إعادة توجيه حسب الدور
switch ($user['role']) {
    case 'admin':
        header('Location: admin.php');
        break;
    case 'supervisor':
        header('Location: supervisor.php');
        break;
    case 'employee':
        header('Location: employee.php');
        break;
    default:
        header('Location: ../index.php');
        break;
}
exit();
?>
