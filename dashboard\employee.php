<?php
require_once '../includes/auth.php';
require_once '../config/database.php';

requireLogin();
requireRole('employee');

$auth = new Auth();
$user = $auth->getCurrentUser();
$database = new Database();
$conn = $database->getConnection();

// إحصائيات المهام للموظف
$stats = [];

// إجمالي المهام المكلف بها
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE assigned_to = ?");
$stmt->execute([$user['id']]);
$stats['total_tasks'] = $stmt->fetch()['count'];

// المهام المكتملة
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE assigned_to = ? AND status = 'completed'");
$stmt->execute([$user['id']]);
$stats['completed_tasks'] = $stmt->fetch()['count'];

// المهام قيد التنفيذ
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE assigned_to = ? AND status = 'in_progress'");
$stmt->execute([$user['id']]);
$stats['in_progress_tasks'] = $stmt->fetch()['count'];

// المهام المتأخرة
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE assigned_to = ? AND due_date < CURDATE() AND status != 'completed'");
$stmt->execute([$user['id']]);
$stats['overdue_tasks'] = $stmt->fetch()['count'];

// مهام الموظف
$stmt = $conn->prepare("
    SELECT t.*, u.full_name as assigned_by_name 
    FROM tasks t 
    LEFT JOIN users u ON t.assigned_by = u.id 
    WHERE t.assigned_to = ? 
    ORDER BY 
        CASE 
            WHEN t.status = 'pending' THEN 1
            WHEN t.status = 'in_progress' THEN 2
            WHEN t.status = 'completed' THEN 3
            WHEN t.status = 'cancelled' THEN 4
        END,
        t.due_date ASC
");
$stmt->execute([$user['id']]);
$my_tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

// معالجة تحديث حالة المهمة
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_task'])) {
    $task_id = $_POST['task_id'];
    $new_status = $_POST['status'];
    
    try {
        $sql = "UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP";
        $params = [$new_status, $task_id];
        
        if ($new_status === 'completed') {
            $sql .= ", completion_date = CURRENT_TIMESTAMP";
        }
        
        $sql .= " WHERE id = ? AND assigned_to = ?";
        $params[] = $user['id'];
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        // إضافة إشعار للمشرف والمدير
        if ($new_status === 'completed') {
            // الحصول على معلومات المهمة
            $stmt = $conn->prepare("SELECT title, assigned_by, supervisor_id FROM tasks WHERE id = ?");
            $stmt->execute([$task_id]);
            $task_info = $stmt->fetch();
            
            // إشعار للمدير
            $stmt = $conn->prepare("INSERT INTO notifications (user_id, title, message, type) VALUES (?, ?, ?, 'task_completed')");
            $stmt->execute([
                $task_info['assigned_by'],
                'تم إكمال مهمة',
                'تم إكمال المهمة: ' . $task_info['title'] . ' بواسطة ' . $user['full_name']
            ]);
            
            // إشعار للمشرف إذا وجد
            if ($task_info['supervisor_id']) {
                $stmt->execute([
                    $task_info['supervisor_id'],
                    'تم إكمال مهمة',
                    'تم إكمال المهمة: ' . $task_info['title'] . ' بواسطة ' . $user['full_name']
                ]);
            }
        }
        
        $message = 'تم تحديث حالة المهمة بنجاح';
        
        // إعادة تحميل الصفحة لإظهار التحديثات
        header('Location: employee.php?success=1');
        exit();
        
    } catch(PDOException $e) {
        $message = 'خطأ في تحديث المهمة: ' . $e->getMessage();
    }
}

// رسالة النجاح من URL
if (isset($_GET['success'])) {
    $message = 'تم تحديث حالة المهمة بنجاح';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الموظف - نظام إدارة المهام</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow-lg);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
        }
        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color), #059669);
        }
        .stat-card.danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
        }
        .stat-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .task-card {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 15px;
            overflow: hidden;
            border-right: 4px solid var(--primary-color);
        }
        .task-card.overdue {
            border-right-color: var(--danger-color);
        }
        .task-card.completed {
            border-right-color: var(--success-color);
            opacity: 0.8;
        }
        .task-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .task-title {
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }
        .task-body {
            padding: 20px;
        }
        .task-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .meta-item {
            font-size: 14px;
        }
        .meta-label {
            color: var(--text-light);
            font-weight: 500;
        }
        .meta-value {
            color: var(--text-dark);
            margin-top: 2px;
        }
        .task-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn-sm {
            padding: 8px 16px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- شريط التنقل -->
        <nav class="navbar">
            <div class="navbar-content">
                <a href="#" class="navbar-brand">📋 نظام إدارة المهام</a>
                <div class="navbar-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($user['full_name']); ?></div>
                        <div class="user-role">موظف</div>
                    </div>
                    <a href="../includes/logout.php" class="btn btn-danger">تسجيل الخروج</a>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">مهامي</h1>
                <p class="page-subtitle">مرحباً <?php echo htmlspecialchars($user['full_name']); ?>، إليك المهام المكلف بها</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- الإحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_tasks']; ?></div>
                    <div class="stat-label">إجمالي المهام</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number"><?php echo $stats['in_progress_tasks']; ?></div>
                    <div class="stat-label">قيد التنفيذ</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-number"><?php echo $stats['completed_tasks']; ?></div>
                    <div class="stat-label">مكتملة</div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-number"><?php echo $stats['overdue_tasks']; ?></div>
                    <div class="stat-label">متأخرة</div>
                </div>
            </div>

            <!-- قائمة المهام -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة المهام</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($my_tasks)): ?>
                        <div class="text-center">
                            <p>لا توجد مهام مكلف بها حالياً</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($my_tasks as $task): ?>
                            <?php
                            $is_overdue = $task['due_date'] && $task['due_date'] < date('Y-m-d') && $task['status'] !== 'completed';
                            $card_class = '';
                            if ($is_overdue) $card_class = 'overdue';
                            if ($task['status'] === 'completed') $card_class = 'completed';
                            ?>
                            <div class="task-card <?php echo $card_class; ?>">
                                <div class="task-header">
                                    <h4 class="task-title"><?php echo htmlspecialchars($task['title']); ?></h4>
                                    <?php
                                    $status_class = '';
                                    $status_text = '';
                                    switch ($task['status']) {
                                        case 'pending':
                                            $status_class = 'badge-warning';
                                            $status_text = 'في الانتظار';
                                            break;
                                        case 'in_progress':
                                            $status_class = 'badge-secondary';
                                            $status_text = 'قيد التنفيذ';
                                            break;
                                        case 'completed':
                                            $status_class = 'badge-success';
                                            $status_text = 'مكتملة';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'badge-danger';
                                            $status_text = 'ملغية';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                </div>
                                <div class="task-body">
                                    <?php if ($task['description']): ?>
                                        <p style="margin-bottom: 15px; color: var(--text-dark);">
                                            <?php echo nl2br(htmlspecialchars($task['description'])); ?>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <div class="task-meta">
                                        <div class="meta-item">
                                            <div class="meta-label">مكلف من</div>
                                            <div class="meta-value"><?php echo htmlspecialchars($task['assigned_by_name']); ?></div>
                                        </div>
                                        <div class="meta-item">
                                            <div class="meta-label">تاريخ البداية</div>
                                            <div class="meta-value"><?php echo $task['start_date'] ? date('Y-m-d', strtotime($task['start_date'])) : 'غير محدد'; ?></div>
                                        </div>
                                        <div class="meta-item">
                                            <div class="meta-label">تاريخ الانتهاء</div>
                                            <div class="meta-value <?php echo $is_overdue ? 'text-danger' : ''; ?>">
                                                <?php echo $task['due_date'] ? date('Y-m-d', strtotime($task['due_date'])) : 'غير محدد'; ?>
                                                <?php if ($is_overdue): ?>
                                                    <span style="color: var(--danger-color); font-weight: bold;"> (متأخرة)</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="meta-item">
                                            <div class="meta-label">الأولوية</div>
                                            <div class="meta-value">
                                                <?php
                                                $priority_text = '';
                                                switch ($task['priority']) {
                                                    case 'low': $priority_text = 'منخفضة'; break;
                                                    case 'medium': $priority_text = 'متوسطة'; break;
                                                    case 'high': $priority_text = 'عالية'; break;
                                                    case 'urgent': $priority_text = 'عاجلة'; break;
                                                }
                                                echo $priority_text;
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <?php if ($task['status'] !== 'completed' && $task['status'] !== 'cancelled'): ?>
                                        <div class="task-actions">
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                                <input type="hidden" name="status" value="in_progress">
                                                <button type="submit" name="update_task" class="btn btn-warning btn-sm"
                                                        <?php echo $task['status'] === 'in_progress' ? 'disabled' : ''; ?>>
                                                    <?php echo $task['status'] === 'in_progress' ? 'قيد التنفيذ' : 'بدء التنفيذ'; ?>
                                                </button>
                                            </form>
                                            
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                                <input type="hidden" name="status" value="completed">
                                                <button type="submit" name="update_task" class="btn btn-success btn-sm"
                                                        onclick="return confirm('هل أنت متأكد من إكمال هذه المهمة؟')">
                                                    إكمال المهمة
                                                </button>
                                            </form>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
