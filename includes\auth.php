<?php
session_start();
require_once '../config/database.php';

class Auth {
    private $conn;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    // تسجيل الدخول
    public function login($username, $password) {
        try {
            $sql = "SELECT id, username, email, password, full_name, role, is_active 
                    FROM users WHERE (username = ? OR email = ?) AND is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$username, $username]);
            
            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (password_verify($password, $user['password'])) {
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['full_name'] = $user['full_name'];
                    $_SESSION['role'] = $user['role'];
                    $_SESSION['email'] = $user['email'];
                    
                    return [
                        'success' => true,
                        'message' => 'تم تسجيل الدخول بنجاح',
                        'user' => $user
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'كلمة المرور غير صحيحة'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم غير موجود أو الحساب غير مفعل'
                ];
            }
        } catch(PDOException $exception) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $exception->getMessage()
            ];
        }
    }
    
    // تسجيل الخروج
    public function logout() {
        session_destroy();
        return true;
    }
    
    // التحقق من تسجيل الدخول
    public function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }
    
    // التحقق من الصلاحيات
    public function hasRole($role) {
        return isset($_SESSION['role']) && $_SESSION['role'] === $role;
    }
    
    // التحقق من صلاحيات متعددة
    public function hasAnyRole($roles) {
        return isset($_SESSION['role']) && in_array($_SESSION['role'], $roles);
    }
    
    // الحصول على معلومات المستخدم الحالي
    public function getCurrentUser() {
        if ($this->isLoggedIn()) {
            return [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'full_name' => $_SESSION['full_name'],
                'role' => $_SESSION['role'],
                'email' => $_SESSION['email']
            ];
        }
        return null;
    }
    
    // إنشاء مستخدم جديد
    public function createUser($data) {
        try {
            $sql = "INSERT INTO users (username, email, password, full_name, role, department, phone) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $this->conn->prepare($sql);
            
            $hashed_password = password_hash($data['password'], PASSWORD_DEFAULT);
            
            $stmt->execute([
                $data['username'],
                $data['email'],
                $hashed_password,
                $data['full_name'],
                $data['role'],
                $data['department'] ?? null,
                $data['phone'] ?? null
            ]);
            
            return [
                'success' => true,
                'message' => 'تم إنشاء المستخدم بنجاح',
                'user_id' => $this->conn->lastInsertId()
            ];
        } catch(PDOException $exception) {
            if ($exception->getCode() == 23000) {
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً'
                ];
            }
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء المستخدم: ' . $exception->getMessage()
            ];
        }
    }
    
    // الحصول على جميع المستخدمين
    public function getAllUsers($role = null) {
        try {
            $sql = "SELECT id, username, email, full_name, role, department, phone, is_active, created_at 
                    FROM users";
            $params = [];
            
            if ($role) {
                $sql .= " WHERE role = ?";
                $params[] = $role;
            }
            
            $sql .= " ORDER BY full_name";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            return [];
        }
    }
    
    // تحديث حالة المستخدم
    public function updateUserStatus($user_id, $is_active) {
        try {
            $sql = "UPDATE users SET is_active = ? WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$is_active, $user_id]);
            
            return [
                'success' => true,
                'message' => 'تم تحديث حالة المستخدم بنجاح'
            ];
        } catch(PDOException $exception) {
            return [
                'success' => false,
                'message' => 'خطأ في تحديث حالة المستخدم'
            ];
        }
    }
}

// دالة للتحقق من تسجيل الدخول وإعادة التوجيه
function requireLogin() {
    $auth = new Auth();
    if (!$auth->isLoggedIn()) {
        header('Location: /index.php');
        exit();
    }
}

// دالة للتحقق من الصلاحيات
function requireRole($role) {
    $auth = new Auth();
    if (!$auth->hasRole($role)) {
        header('Location: /dashboard/');
        exit();
    }
}
?>
