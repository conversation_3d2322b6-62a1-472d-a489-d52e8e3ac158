<?php
require_once dirname(__DIR__) . '/includes/auth.php';
require_once dirname(__DIR__) . '/config/database.php';

requireLogin();
requireRole('admin');

$auth = new Auth();
$user = $auth->getCurrentUser();
$database = new Database();
$conn = $database->getConnection();

// الحصول على جميع المستخدمين للقوائم المنسدلة
$employees = $auth->getAllUsers('employee');
$supervisors = $auth->getAllUsers('supervisor');

// معالجة إنشاء مهمة جديدة
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_task'])) {
    try {
        $sql = "INSERT INTO tasks (title, description, assigned_to, assigned_by, supervisor_id, priority, start_date, due_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);

        $supervisor_id = !empty($_POST['supervisor_id']) ? $_POST['supervisor_id'] : null;

        $stmt->execute([
            $_POST['title'],
            $_POST['description'],
            $_POST['assigned_to'],
            $user['id'],
            $supervisor_id,
            $_POST['priority'],
            $_POST['start_date'],
            $_POST['due_date']
        ]);

        // إضافة إشعار للموظف
        $stmt = $conn->prepare("INSERT INTO notifications (user_id, title, message, type) VALUES (?, ?, ?, 'task_assigned')");
        $stmt->execute([
            $_POST['assigned_to'],
            'مهمة جديدة',
            'تم تكليفك بمهمة جديدة: ' . $_POST['title']
        ]);

        // إضافة إشعار للمشرف إذا وجد
        if ($supervisor_id) {
            $stmt->execute([
                $supervisor_id,
                'مهمة جديدة للإشراف',
                'تم تكليفك بالإشراف على مهمة: ' . $_POST['title']
            ]);
        }

        $message = 'تم إنشاء المهمة بنجاح';
        header('Location: tasks.php?success=1');
        exit();

    } catch(PDOException $e) {
        $message = 'خطأ في إنشاء المهمة: ' . $e->getMessage();
    }
}

// معالجة تحديث حالة المهمة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_task'])) {
    try {
        $sql = "UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP";
        $params = [$_POST['status'], $_POST['task_id']];

        if ($_POST['status'] === 'completed') {
            $sql .= ", completion_date = CURRENT_TIMESTAMP";
        }

        $sql .= " WHERE id = ?";

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);

        $message = 'تم تحديث حالة المهمة بنجاح';
        header('Location: tasks.php?success=1');
        exit();

    } catch(PDOException $e) {
        $message = 'خطأ في تحديث المهمة: ' . $e->getMessage();
    }
}

// الحصول على جميع المهام
$stmt = $conn->prepare("
    SELECT t.*,
           u1.full_name as assigned_to_name,
           u2.full_name as assigned_by_name,
           u3.full_name as supervisor_name
    FROM tasks t
    LEFT JOIN users u1 ON t.assigned_to = u1.id
    LEFT JOIN users u2 ON t.assigned_by = u2.id
    LEFT JOIN users u3 ON t.supervisor_id = u3.id
    ORDER BY t.created_at DESC
");
$stmt->execute();
$all_tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

// رسالة النجاح من URL
if (isset($_GET['success'])) {
    $message = 'تم تنفيذ العملية بنجاح';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المهام - نظام إدارة المهام</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: var(--white);
            margin: 2% auto;
            padding: 0;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 600px;
            box-shadow: var(--shadow-lg);
            max-height: 90vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px 25px;
            background: var(--primary-color);
            color: var(--white);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }
        .modal-body {
            padding: 25px;
        }
        .close {
            color: var(--white);
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            opacity: 0.7;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
        .task-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        .priority-high {
            color: var(--danger-color);
            font-weight: bold;
        }
        .priority-urgent {
            color: var(--danger-color);
            font-weight: bold;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- شريط التنقل -->
        <nav class="navbar">
            <div class="navbar-content">
                <a href="admin.php" class="navbar-brand">📋 نظام إدارة المهام</a>
                <div class="navbar-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($user['full_name']); ?></div>
                        <div class="user-role">مدير النظام</div>
                    </div>
                    <a href="../includes/logout.php" class="btn btn-danger">تسجيل الخروج</a>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">إدارة المهام</h1>
                <p class="page-subtitle">إنشاء وإدارة مهام الموظفين</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- زر إضافة مهمة جديدة -->
            <div class="mb-3">
                <button onclick="openModal('taskModal')" class="btn btn-primary">
                    ➕ إضافة مهمة جديدة
                </button>
                <a href="admin.php" class="btn btn-secondary">
                    ← العودة للوحة التحكم
                </a>
            </div>

            <!-- قائمة المهام -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">جميع المهام</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($all_tasks)): ?>
                        <div class="text-center">
                            <p>لا توجد مهام حالياً</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>عنوان المهمة</th>
                                        <th>المكلف</th>
                                        <th>المشرف</th>
                                        <th>الأولوية</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الانتهاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($all_tasks as $task): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($task['title']); ?></strong>
                                                <?php if ($task['description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars(substr($task['description'], 0, 50)) . '...'; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($task['assigned_to_name']); ?></td>
                                            <td><?php echo $task['supervisor_name'] ? htmlspecialchars($task['supervisor_name']) : 'غير محدد'; ?></td>
                                            <td>
                                                <?php
                                                $priority_class = '';
                                                $priority_text = '';
                                                switch ($task['priority']) {
                                                    case 'low':
                                                        $priority_text = 'منخفضة';
                                                        break;
                                                    case 'medium':
                                                        $priority_text = 'متوسطة';
                                                        break;
                                                    case 'high':
                                                        $priority_class = 'priority-high';
                                                        $priority_text = 'عالية';
                                                        break;
                                                    case 'urgent':
                                                        $priority_class = 'priority-urgent';
                                                        $priority_text = 'عاجلة';
                                                        break;
                                                }
                                                ?>
                                                <span class="<?php echo $priority_class; ?>"><?php echo $priority_text; ?></span>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';
                                                switch ($task['status']) {
                                                    case 'pending':
                                                        $status_class = 'badge-warning';
                                                        $status_text = 'في الانتظار';
                                                        break;
                                                    case 'in_progress':
                                                        $status_class = 'badge-secondary';
                                                        $status_text = 'قيد التنفيذ';
                                                        break;
                                                    case 'completed':
                                                        $status_class = 'badge-success';
                                                        $status_text = 'مكتملة';
                                                        break;
                                                    case 'cancelled':
                                                        $status_class = 'badge-danger';
                                                        $status_text = 'ملغية';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                            <td>
                                                <?php
                                                if ($task['due_date']) {
                                                    $due_date = date('Y-m-d', strtotime($task['due_date']));
                                                    $is_overdue = $task['due_date'] < date('Y-m-d') && $task['status'] !== 'completed';
                                                    echo '<span class="' . ($is_overdue ? 'text-danger' : '') . '">' . $due_date . '</span>';
                                                    if ($is_overdue) {
                                                        echo '<br><small class="text-danger">متأخرة</small>';
                                                    }
                                                } else {
                                                    echo 'غير محدد';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <div class="task-actions">
                                                    <?php if ($task['status'] !== 'completed' && $task['status'] !== 'cancelled'): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                                            <input type="hidden" name="status" value="completed">
                                                            <button type="submit" name="update_task" class="btn btn-success btn-sm"
                                                                    onclick="return confirm('هل أنت متأكد من إكمال هذه المهمة؟')">
                                                                إكمال
                                                            </button>
                                                        </form>

                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                                            <input type="hidden" name="status" value="cancelled">
                                                            <button type="submit" name="update_task" class="btn btn-danger btn-sm"
                                                                    onclick="return confirm('هل أنت متأكد من إلغاء هذه المهمة؟')">
                                                                إلغاء
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مهمة -->
    <div id="taskModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="closeModal('taskModal')">&times;</span>
                <h2>إضافة مهمة جديدة</h2>
            </div>
            <div class="modal-body">
                <form method="POST">
                    <div class="form-group">
                        <label for="title">عنوان المهمة *</label>
                        <input type="text" id="title" name="title" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="description">وصف المهمة</label>
                        <textarea id="description" name="description" class="form-control" rows="4"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="assigned_to">المكلف *</label>
                            <select id="assigned_to" name="assigned_to" class="form-control" required>
                                <option value="">اختر الموظف</option>
                                <?php foreach ($employees as $employee): ?>
                                    <option value="<?php echo $employee['id']; ?>">
                                        <?php echo htmlspecialchars($employee['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="supervisor_id">المشرف</label>
                            <select id="supervisor_id" name="supervisor_id" class="form-control">
                                <option value="">اختر المشرف (اختياري)</option>
                                <?php foreach ($supervisors as $supervisor): ?>
                                    <option value="<?php echo $supervisor['id']; ?>">
                                        <?php echo htmlspecialchars($supervisor['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="priority">الأولوية</label>
                            <select id="priority" name="priority" class="form-control">
                                <option value="low">منخفضة</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="start_date">تاريخ البداية</label>
                            <input type="date" id="start_date" name="start_date" class="form-control" value="<?php echo date('Y-m-d'); ?>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="due_date">تاريخ الانتهاء</label>
                        <input type="date" id="due_date" name="due_date" class="form-control">
                    </div>

                    <button type="submit" name="create_task" class="btn btn-primary">إنشاء المهمة</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // تعيين الحد الأدنى لتاريخ الانتهاء
        document.getElementById('start_date').addEventListener('change', function() {
            document.getElementById('due_date').min = this.value;
        });
    </script>
</body>
</html>
